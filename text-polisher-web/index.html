<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学术文本润色助手</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- 头部导航 -->
        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">
                    <i class="fas fa-magic"></i>
                    学术文本润色助手
                </h1>
                <div class="header-actions">
                    <button id="theme-toggle" class="icon-btn" title="切换主题">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button id="settings-toggle" class="icon-btn" title="设置">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- 设置面板 -->
        <div id="settings-panel" class="settings-panel">
            <div class="settings-content">
                <h3>API 设置</h3>
                <div class="form-group">
                    <label for="api-key">Gemini API 密钥</label>
                    <div class="input-group">
                        <input type="password" id="api-key" placeholder="请输入您的 Gemini API 密钥">
                        <button id="toggle-api-key" class="toggle-btn">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="help-text">
                        API 密钥将安全地存储在您的浏览器本地，不会上传到服务器
                    </small>
                </div>
                <div class="form-group">
                    <label for="model-input">模型名称</label>
                    <input type="text" id="model-input" placeholder="请输入 Gemini 模型名称，如：gemini-1.5-pro-latest">
                    <small class="help-text">
                        常用模型：gemini-1.5-pro-latest、gemini-1.5-flash-latest、gemini-pro
                    </small>
                </div>
                <div class="form-actions">
                    <button id="save-settings" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        保存设置
                    </button>
                    <button id="test-api" class="btn btn-secondary">
                        <i class="fas fa-flask"></i>
                        测试连接
                    </button>
                </div>
                <div id="api-status" class="api-status"></div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 输入区域 -->
            <section class="input-section">
                <div class="section-header">
                    <h2>输入文本</h2>
                    <div class="section-actions">
                        <button id="clear-input" class="btn btn-ghost">
                            <i class="fas fa-trash"></i>
                            清空
                        </button>
                        <button id="paste-text" class="btn btn-ghost">
                            <i class="fas fa-paste"></i>
                            粘贴
                        </button>
                    </div>
                </div>
                <div class="input-container">
                    <textarea 
                        id="input-text" 
                        placeholder="请输入需要润色的学术文本..."
                        rows="10"
                    ></textarea>
                    <div class="input-footer">
                        <span id="char-count" class="char-count">0 字符</span>
                        <button id="polish-btn" class="btn btn-primary btn-large">
                            <i class="fas fa-magic"></i>
                            开始润色
                        </button>
                    </div>
                </div>
            </section>

            <!-- 输出区域 -->
            <section class="output-section">
                <div class="section-header">
                    <h2>润色结果</h2>
                    <div class="section-actions">
                        <button id="copy-all" class="btn btn-ghost" style="display: none;">
                            <i class="fas fa-copy"></i>
                            复制全部
                        </button>
                    </div>
                </div>
                <div id="output-container" class="output-container">
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>润色结果将在这里显示</p>
                        <small>请先输入文本并点击"开始润色"按钮</small>
                    </div>
                </div>
            </section>
        </main>

        <!-- 加载遮罩 -->
        <div id="loading-overlay" class="loading-overlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>正在润色文本，请稍候...</p>
                <small>这可能需要几秒钟时间</small>
            </div>
        </div>

        <!-- 通知容器 -->
        <div id="notification-container" class="notification-container"></div>
    </div>

    <!-- JavaScript 文件 -->
    <script src="js/storage.js"></script>
    <script src="js/gemini-api.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
