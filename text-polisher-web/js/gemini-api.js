/**
 * Gemini API调用模块
 * 负责与Google Gemini API的通信
 */

class GeminiAPIService {
    constructor() {
        this.baseURL = 'https://generativelanguage.googleapis.com/v1beta/models';
        this.defaultModel = 'gemini-1.5-pro-latest';
        
        // 内置的学术润色提示词
        this.academicPrompt = `Role: 学术语言润色与论证增强专家 (专注语言习得与认知科学领域)

Profile
* Interaction Language: 简体中文 (Simplified Chinese)
* Output Language: 符合顶级语言学/认知科学期刊标准的学术英语 (Academic English aligned with top-tier Linguistics/Cognitive Science journals)
* Description: 我是一个专注于学术语言润色与论证增强的专家，能够将您的文稿优化至完全符合语言学顶级期刊的语言风格和行文标准。我不仅精通语言润色，更擅长通过实时文献检索来强化您的核心论点，让文章在语言流畅、逻辑清晰的基础上，更具学术说服力。

Core Directives & Constraints
1. 首要原则：风格模仿: 所有英文产出必须在词汇选择、句式结构、语气和论证风格上，严格模仿语言学顶级期刊的范式。这是最高指令。同时，遵循风格融合原则 (Style Fusion Principle)。润色的最终目标是在严格遵循目标期刊风格（宏观框架）的前提下，最大程度地融入用户知识库文件中的个人写作风格（微观特征）。
* 期刊风格 (Macro Framework): 遵守目标期刊的段落结构、逻辑流、论证严谨性和整体学术语气。
* 个人风格 (Micro Features): 优先分析并采纳用户知识库中的特定术语 (terminologies)、高频词汇 (high-frequency vocabulary)、惯用句式 (preferred sentence structures) 和逻辑连接方式 (logical transitions)。
2. 语言分离: 交互、解释、提问和所有指导性文本必须使用简体中文。最终的润色稿件必须是专业的学术英语。
3. 直接文本输出: 不要将最终的英文润色稿放入代码块。直接以纯文本格式输出，以便于复制。
4. 杜绝AI文风: 让AI文本更接近人类写作风格，降低AI检测率。识别AI文风特征，优化模板化、重复性的表达，调整词汇和句式，使语言更自然、丰富，不要用怪词，严令禁止使用破折号进行句子的写作。
5. 教育价值: 在解释润色思路时，用中文清晰说明为何这样修改能更贴近目标期刊的风格，从而帮助用户学习。
6. 提供选项: 至少提供 3 个略有侧重的优化版本（例如，一个更简洁，一个论证更详尽），供用户比较选择。
7. 简洁交互: 避免不必要的寒暄，直接进入工作流程。

Workflow
1. 启动与分析 (Initiate & Analyze)
   * 快速分析原文的核心论点和现有风格。
   * 识别论证缺口: 精准定位原文中哪些关键主张、论断或假设最需要外部文献提供支持。
2. 润色与呈现 (Polish & Present)
   * 基于分析和文献整合，选择 3 种润色方案。每种方案都用中文简要说明其风格侧重（例如："此方案更贴近...的严谨简洁风格，并引用了 [Author, Year] 的研究来强化论证"）。
   * 对经过文献增强后的文本进行深度语言优化，产出 3 个完整的【学术英语】润色定稿版本。
   * 直接以纯文本格式呈现所有版本。
   * 在每个版本后，用**【中文】**以要点形式，精炼地解释：
     * 语言修改: 关键的词汇和句式修改思路。

请对以下文本进行学术润色：

`;
    }

    /**
     * 测试API连接
     * @param {string} apiKey - API密钥
     * @param {string} model - 模型名称
     * @returns {Promise<boolean>} 连接是否成功
     */
    async testConnection(apiKey, model = this.defaultModel) {
        try {
            const response = await this.makeRequest(apiKey, model, '测试连接');
            return response.success;
        } catch (error) {
            console.error('API连接测试失败:', error);
            return false;
        }
    }

    /**
     * 润色文本
     * @param {string} text - 要润色的文本
     * @param {string} apiKey - API密钥
     * @param {string} model - 模型名称
     * @returns {Promise<object>} 润色结果
     */
    async polishText(text, apiKey, model = this.defaultModel) {
        if (!text || !text.trim()) {
            throw new Error('请输入要润色的文本');
        }

        if (!apiKey) {
            throw new Error('请先配置API密钥');
        }

        const prompt = this.academicPrompt + text.trim();
        
        try {
            const response = await this.makeRequest(apiKey, model, prompt);
            
            if (!response.success) {
                throw new Error(response.error || '润色请求失败');
            }

            return this.parsePolishResult(response.data);
        } catch (error) {
            console.error('文本润色失败:', error);
            throw error;
        }
    }

    /**
     * 发送API请求
     * @param {string} apiKey - API密钥
     * @param {string} model - 模型名称
     * @param {string} prompt - 提示词
     * @returns {Promise<object>} API响应
     */
    async makeRequest(apiKey, model, prompt) {
        const url = `${this.baseURL}/${model}:generateContent?key=${apiKey}`;
        
        const requestBody = {
            contents: [{
                parts: [{
                    text: prompt
                }]
            }],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 8192
            },
            safetySettings: [
                {
                    category: "HARM_CATEGORY_HARASSMENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_HATE_SPEECH",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                },
                {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                    threshold: "BLOCK_MEDIUM_AND_ABOVE"
                }
            ]
        };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            const data = await response.json();

            if (!response.ok) {
                return {
                    success: false,
                    error: data.error?.message || `HTTP ${response.status}: ${response.statusText}`
                };
            }

            if (data.error) {
                return {
                    success: false,
                    error: data.error.message
                };
            }

            if (!data.candidates || data.candidates.length === 0) {
                return {
                    success: false,
                    error: '未收到有效响应'
                };
            }

            const candidate = data.candidates[0];
            if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {
                return {
                    success: false,
                    error: '响应内容为空'
                };
            }

            return {
                success: true,
                data: candidate.content.parts[0].text
            };

        } catch (error) {
            return {
                success: false,
                error: error.message || '网络请求失败'
            };
        }
    }

    /**
     * 解析润色结果
     * @param {string} rawText - API返回的原始文本
     * @returns {object} 解析后的结果
     */
    parsePolishResult(rawText) {
        try {
            // 尝试解析多个版本的润色结果
            const versions = this.extractVersions(rawText);
            
            if (versions.length === 0) {
                // 如果无法解析出多个版本，将整个文本作为单一版本
                return {
                    versions: [{
                        title: '润色版本',
                        content: rawText.trim(),
                        explanation: '完整的润色结果'
                    }],
                    originalLength: 0,
                    totalVersions: 1
                };
            }

            return {
                versions: versions,
                originalLength: 0,
                totalVersions: versions.length
            };

        } catch (error) {
            console.error('解析润色结果失败:', error);
            return {
                versions: [{
                    title: '润色结果',
                    content: rawText,
                    explanation: '原始响应内容'
                }],
                originalLength: 0,
                totalVersions: 1
            };
        }
    }

    /**
     * 从文本中提取多个版本
     * @param {string} text - 原始文本
     * @returns {Array} 版本数组
     */
    extractVersions(text) {
        const versions = [];
        
        // 尝试按照不同的模式分割文本
        const patterns = [
            /版本\s*[一二三1-3][:：]/gi,
            /方案\s*[一二三1-3][:：]/gi,
            /选项\s*[一二三1-3][:：]/gi,
            /\d+\.\s*(?=\S)/g
        ];

        for (const pattern of patterns) {
            const matches = text.split(pattern);
            if (matches.length > 1) {
                matches.forEach((match, index) => {
                    if (index === 0 && !match.trim()) return;
                    
                    const content = match.trim();
                    if (content.length > 50) { // 过滤太短的内容
                        versions.push({
                            title: `润色版本 ${versions.length + 1}`,
                            content: content,
                            explanation: this.extractExplanation(content)
                        });
                    }
                });
                
                if (versions.length > 1) {
                    return versions.slice(0, 3); // 最多返回3个版本
                }
            }
        }

        return versions;
    }

    /**
     * 提取解释说明
     * @param {string} content - 内容文本
     * @returns {string} 解释说明
     */
    extractExplanation(content) {
        // 查找中文解释部分
        const explanationPatterns = [
            /【中文】(.*?)(?=\n\n|\n$|$)/s,
            /解释[:：](.*?)(?=\n\n|\n$|$)/s,
            /说明[:：](.*?)(?=\n\n|\n$|$)/s
        ];

        for (const pattern of explanationPatterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }

        return '语言优化和学术表达改进';
    }

    /**
     * 获取支持的模型列表
     * @returns {Array} 模型列表
     */
    getSupportedModels() {
        return [
            {
                id: 'gemini-1.5-pro-latest',
                name: 'Gemini 1.5 Pro (推荐)',
                description: '最强大的模型，适合复杂的文本润色任务'
            },
            {
                id: 'gemini-1.5-flash-latest',
                name: 'Gemini 1.5 Flash (快速)',
                description: '快速响应，适合简单的文本处理'
            },
            {
                id: 'gemini-pro',
                name: 'Gemini Pro',
                description: '平衡性能和速度的通用模型'
            }
        ];
    }
}

// 创建全局API服务实例
window.geminiAPI = new GeminiAPIService();
