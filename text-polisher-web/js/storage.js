/**
 * 本地存储管理模块
 * 负责API密钥和用户设置的安全存储
 */

class StorageManager {
    constructor() {
        this.prefix = 'textPolisher_';
        this.keys = {
            apiKey: 'apiKey',
            selectedModel: 'selectedModel',
            theme: 'theme',
            lastInputText: 'lastInputText'
        };
    }

    /**
     * 保存数据到本地存储
     * @param {string} key - 存储键名
     * @param {any} value - 要存储的值
     * @returns {boolean} 是否保存成功
     */
    save(key, value) {
        try {
            const fullKey = this.prefix + key;
            if (typeof value === 'object') {
                localStorage.setItem(fullKey, JSON.stringify(value));
            } else {
                localStorage.setItem(fullKey, value);
            }
            return true;
        } catch (error) {
            console.error('保存数据失败:', error);
            return false;
        }
    }

    /**
     * 从本地存储读取数据
     * @param {string} key - 存储键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 读取的值或默认值
     */
    load(key, defaultValue = null) {
        try {
            const fullKey = this.prefix + key;
            const value = localStorage.getItem(fullKey);
            
            if (value === null) {
                return defaultValue;
            }

            // 尝试解析JSON
            try {
                return JSON.parse(value);
            } catch {
                return value;
            }
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除存储的数据
     * @param {string} key - 存储键名
     * @returns {boolean} 是否删除成功
     */
    remove(key) {
        try {
            const fullKey = this.prefix + key;
            localStorage.removeItem(fullKey);
            return true;
        } catch (error) {
            console.error('删除数据失败:', error);
            return false;
        }
    }

    /**
     * 清空所有应用数据
     * @returns {boolean} 是否清空成功
     */
    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 保存API密钥（加密存储）
     * @param {string} apiKey - API密钥
     * @returns {boolean} 是否保存成功
     */
    saveAPIKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            return false;
        }
        
        // 简单的编码处理（注意：这不是真正的加密，只是基础的混淆）
        const encoded = btoa(apiKey);
        return this.save(this.keys.apiKey, encoded);
    }

    /**
     * 读取API密钥（解密）
     * @returns {string|null} API密钥或null
     */
    loadAPIKey() {
        try {
            const encoded = this.load(this.keys.apiKey);
            if (!encoded) {
                return null;
            }
            return atob(encoded);
        } catch (error) {
            console.error('读取API密钥失败:', error);
            return null;
        }
    }

    /**
     * 验证API密钥格式
     * @param {string} apiKey - API密钥
     * @returns {boolean} 是否为有效格式
     */
    validateAPIKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') {
            return false;
        }
        
        // Gemini API密钥通常以"AIza"开头，长度约为39个字符
        return apiKey.startsWith('AIza') && apiKey.length >= 35;
    }

    /**
     * 保存选中的模型
     * @param {string} model - 模型名称
     * @returns {boolean} 是否保存成功
     */
    saveSelectedModel(model) {
        return this.save(this.keys.selectedModel, model);
    }

    /**
     * 读取选中的模型
     * @returns {string} 模型名称
     */
    loadSelectedModel() {
        return this.load(this.keys.selectedModel, 'gemini-1.5-pro-latest');
    }

    /**
     * 保存主题设置
     * @param {string} theme - 主题名称 ('light' 或 'dark')
     * @returns {boolean} 是否保存成功
     */
    saveTheme(theme) {
        return this.save(this.keys.theme, theme);
    }

    /**
     * 读取主题设置
     * @returns {string} 主题名称
     */
    loadTheme() {
        return this.load(this.keys.theme, 'light');
    }

    /**
     * 保存最后输入的文本（用于恢复）
     * @param {string} text - 输入文本
     * @returns {boolean} 是否保存成功
     */
    saveLastInputText(text) {
        if (text && text.length > 10000) {
            // 限制存储的文本长度
            text = text.substring(0, 10000);
        }
        return this.save(this.keys.lastInputText, text);
    }

    /**
     * 读取最后输入的文本
     * @returns {string} 输入文本
     */
    loadLastInputText() {
        return this.load(this.keys.lastInputText, '');
    }

    /**
     * 获取存储使用情况
     * @returns {object} 存储使用情况信息
     */
    getStorageInfo() {
        try {
            const used = new Blob(Object.values(localStorage)).size;
            const quota = 5 * 1024 * 1024; // 假设5MB配额
            
            return {
                used: used,
                quota: quota,
                percentage: Math.round((used / quota) * 100),
                available: quota - used
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return {
                used: 0,
                quota: 0,
                percentage: 0,
                available: 0
            };
        }
    }

    /**
     * 导出设置（不包含敏感信息）
     * @returns {object} 设置对象
     */
    exportSettings() {
        return {
            selectedModel: this.loadSelectedModel(),
            theme: this.loadTheme(),
            hasAPIKey: !!this.loadAPIKey(),
            exportTime: new Date().toISOString()
        };
    }

    /**
     * 导入设置
     * @param {object} settings - 设置对象
     * @returns {boolean} 是否导入成功
     */
    importSettings(settings) {
        try {
            if (settings.selectedModel) {
                this.saveSelectedModel(settings.selectedModel);
            }
            if (settings.theme) {
                this.saveTheme(settings.theme);
            }
            return true;
        } catch (error) {
            console.error('导入设置失败:', error);
            return false;
        }
    }
}

// 创建全局存储管理器实例
window.storageManager = new StorageManager();
