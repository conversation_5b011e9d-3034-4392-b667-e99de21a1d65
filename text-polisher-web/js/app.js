/**
 * 主应用逻辑
 * 负责UI交互和应用状态管理
 */

class TextPolisherApp {
    constructor() {
        this.isProcessing = false;
        this.currentResults = null;
        
        // 初始化应用
        this.init();
    }

    /**
     * 初始化应用
     */
    init() {
        this.initializeElements();
        this.bindEvents();
        this.loadSettings();
        this.setupTheme();
        this.restoreLastInput();
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        // 主要元素
        this.elements = {
            // 设置相关
            settingsToggle: document.getElementById('settings-toggle'),
            settingsPanel: document.getElementById('settings-panel'),
            apiKeyInput: document.getElementById('api-key'),
            toggleApiKey: document.getElementById('toggle-api-key'),
            modelSelect: document.getElementById('model-select'),
            saveSettings: document.getElementById('save-settings'),
            testAPI: document.getElementById('test-api'),
            apiStatus: document.getElementById('api-status'),
            
            // 主题切换
            themeToggle: document.getElementById('theme-toggle'),
            
            // 输入区域
            inputText: document.getElementById('input-text'),
            charCount: document.getElementById('char-count'),
            clearInput: document.getElementById('clear-input'),
            pasteText: document.getElementById('paste-text'),
            polishBtn: document.getElementById('polish-btn'),
            
            // 输出区域
            outputContainer: document.getElementById('output-container'),
            copyAll: document.getElementById('copy-all'),
            
            // 加载和通知
            loadingOverlay: document.getElementById('loading-overlay'),
            notificationContainer: document.getElementById('notification-container')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 设置面板
        this.elements.settingsToggle.addEventListener('click', () => this.toggleSettings());
        this.elements.toggleApiKey.addEventListener('click', () => this.toggleAPIKeyVisibility());
        this.elements.saveSettings.addEventListener('click', () => this.saveSettings());
        this.elements.testAPI.addEventListener('click', () => this.testAPIConnection());
        
        // 主题切换
        this.elements.themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // 输入区域
        this.elements.inputText.addEventListener('input', () => this.updateCharCount());
        this.elements.inputText.addEventListener('input', () => this.saveCurrentInput());
        this.elements.clearInput.addEventListener('click', () => this.clearInput());
        this.elements.pasteText.addEventListener('click', () => this.pasteFromClipboard());
        this.elements.polishBtn.addEventListener('click', () => this.polishText());
        
        // 输出区域
        this.elements.copyAll.addEventListener('click', () => this.copyAllResults());
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboardShortcuts(e));
        
        // 点击外部关闭设置面板
        document.addEventListener('click', (e) => {
            if (!this.elements.settingsPanel.contains(e.target) && 
                !this.elements.settingsToggle.contains(e.target) &&
                this.elements.settingsPanel.classList.contains('open')) {
                this.toggleSettings();
            }
        });
    }

    /**
     * 加载保存的设置
     */
    loadSettings() {
        // 加载API密钥状态
        const apiKey = storageManager.loadAPIKey();
        if (apiKey) {
            this.elements.apiKeyInput.value = '••••••••••••••••••••••••••••••••••••••••';
            this.updateAPIStatus('success', 'API密钥已配置');
        }
        
        // 加载选中的模型
        const selectedModel = storageManager.loadSelectedModel();
        this.elements.modelSelect.value = selectedModel;
        
        // 更新润色按钮状态
        this.updatePolishButtonState();
    }

    /**
     * 设置主题
     */
    setupTheme() {
        const theme = storageManager.loadTheme();
        this.applyTheme(theme);
    }

    /**
     * 应用主题
     * @param {string} theme - 主题名称
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        const icon = this.elements.themeToggle.querySelector('i');
        icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
    }

    /**
     * 恢复最后输入的文本
     */
    restoreLastInput() {
        const lastInput = storageManager.loadLastInputText();
        if (lastInput) {
            this.elements.inputText.value = lastInput;
            this.updateCharCount();
        }
    }

    /**
     * 切换设置面板
     */
    toggleSettings() {
        this.elements.settingsPanel.classList.toggle('open');
    }

    /**
     * 切换API密钥可见性
     */
    toggleAPIKeyVisibility() {
        const input = this.elements.apiKeyInput;
        const icon = this.elements.toggleApiKey.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
            
            // 如果当前显示的是占位符，清空输入框
            if (input.value.includes('••••')) {
                input.value = storageManager.loadAPIKey() || '';
            }
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        const apiKey = this.elements.apiKeyInput.value.trim();
        const selectedModel = this.elements.modelSelect.value;
        
        // 验证API密钥
        if (apiKey && !apiKey.includes('••••')) {
            if (!storageManager.validateAPIKey(apiKey)) {
                this.showNotification('error', '无效的API密钥', 'API密钥格式不正确，请检查后重试');
                return;
            }
            
            // 保存API密钥
            if (storageManager.saveAPIKey(apiKey)) {
                this.updateAPIStatus('success', 'API密钥已保存');
            } else {
                this.showNotification('error', '保存失败', '无法保存API密钥，请重试');
                return;
            }
        }
        
        // 保存选中的模型
        storageManager.saveSelectedModel(selectedModel);
        
        // 更新UI状态
        this.updatePolishButtonState();
        this.showNotification('success', '设置已保存', '您的配置已成功保存');
        
        // 隐藏API密钥
        if (apiKey && !apiKey.includes('••••')) {
            this.elements.apiKeyInput.value = '••••••••••••••••••••••••••••••••••••••••';
            this.elements.apiKeyInput.type = 'password';
            this.elements.toggleApiKey.querySelector('i').className = 'fas fa-eye';
        }
    }

    /**
     * 测试API连接
     */
    async testAPIConnection() {
        const apiKey = storageManager.loadAPIKey();
        const model = this.elements.modelSelect.value;
        
        if (!apiKey) {
            this.showNotification('warning', '请先配置API密钥', '需要先保存有效的API密钥才能测试连接');
            return;
        }
        
        this.updateAPIStatus('loading', '正在测试连接...');
        
        try {
            const success = await geminiAPI.testConnection(apiKey, model);
            
            if (success) {
                this.updateAPIStatus('success', 'API连接成功！');
                this.showNotification('success', '连接成功', 'API连接测试通过，可以正常使用');
            } else {
                this.updateAPIStatus('error', 'API连接失败，请检查密钥和网络');
                this.showNotification('error', '连接失败', '请检查API密钥是否正确，以及网络连接是否正常');
            }
        } catch (error) {
            this.updateAPIStatus('error', `连接错误: ${error.message}`);
            this.showNotification('error', '连接错误', error.message);
        }
    }

    /**
     * 切换主题
     */
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        this.applyTheme(newTheme);
        storageManager.saveTheme(newTheme);
    }

    /**
     * 更新字符计数
     */
    updateCharCount() {
        const text = this.elements.inputText.value;
        const count = text.length;
        this.elements.charCount.textContent = `${count.toLocaleString()} 字符`;
        
        // 根据字符数量改变颜色
        if (count > 10000) {
            this.elements.charCount.style.color = 'var(--error-color)';
        } else if (count > 5000) {
            this.elements.charCount.style.color = 'var(--warning-color)';
        } else {
            this.elements.charCount.style.color = 'var(--text-muted)';
        }
    }

    /**
     * 保存当前输入
     */
    saveCurrentInput() {
        const text = this.elements.inputText.value;
        storageManager.saveLastInputText(text);
    }

    /**
     * 清空输入
     */
    clearInput() {
        this.elements.inputText.value = '';
        this.updateCharCount();
        this.saveCurrentInput();
        this.elements.inputText.focus();
    }

    /**
     * 从剪贴板粘贴
     */
    async pasteFromClipboard() {
        try {
            const text = await navigator.clipboard.readText();
            this.elements.inputText.value = text;
            this.updateCharCount();
            this.saveCurrentInput();
            this.showNotification('success', '粘贴成功', '文本已从剪贴板粘贴');
        } catch (error) {
            this.showNotification('error', '粘贴失败', '无法访问剪贴板，请手动粘贴');
        }
    }

    /**
     * 润色文本
     */
    async polishText() {
        const text = this.elements.inputText.value.trim();
        
        if (!text) {
            this.showNotification('warning', '请输入文本', '请先输入需要润色的文本');
            this.elements.inputText.focus();
            return;
        }
        
        const apiKey = storageManager.loadAPIKey();
        if (!apiKey) {
            this.showNotification('warning', '请配置API密钥', '请先在设置中配置Gemini API密钥');
            this.toggleSettings();
            return;
        }
        
        if (this.isProcessing) {
            return;
        }
        
        this.isProcessing = true;
        this.showLoading(true);
        this.elements.polishBtn.disabled = true;
        
        try {
            const model = storageManager.loadSelectedModel();
            const result = await geminiAPI.polishText(text, apiKey, model);
            
            this.currentResults = result;
            this.displayResults(result);
            this.showNotification('success', '润色完成', `已生成 ${result.totalVersions} 个润色版本`);
            
        } catch (error) {
            console.error('润色失败:', error);
            this.showNotification('error', '润色失败', error.message);
        } finally {
            this.isProcessing = false;
            this.showLoading(false);
            this.elements.polishBtn.disabled = false;
        }
    }

    /**
     * 显示结果
     * @param {object} result - 润色结果
     */
    displayResults(result) {
        const container = this.elements.outputContainer;
        container.innerHTML = '';
        
        if (!result.versions || result.versions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>未能生成润色结果</p>
                    <small>请检查输入文本或重试</small>
                </div>
            `;
            return;
        }
        
        result.versions.forEach((version, index) => {
            const card = this.createResultCard(version, index + 1);
            container.appendChild(card);
        });
        
        // 显示复制全部按钮
        this.elements.copyAll.style.display = 'flex';
    }

    /**
     * 创建结果卡片
     * @param {object} version - 版本数据
     * @param {number} index - 版本索引
     * @returns {HTMLElement} 卡片元素
     */
    createResultCard(version, index) {
        const card = document.createElement('div');
        card.className = 'result-card';
        
        card.innerHTML = `
            <div class="result-header">
                <span class="result-title">${version.title || `版本 ${index}`}</span>
                <button class="btn copy-btn" onclick="app.copyText('${index}')">
                    <i class="fas fa-copy"></i>
                    复制
                </button>
            </div>
            <div class="result-content">
                <div class="result-text">${version.content}</div>
                ${version.explanation ? `
                    <div class="result-explanation">
                        <h4>修改说明：</h4>
                        <div>${version.explanation}</div>
                    </div>
                ` : ''}
            </div>
        `;
        
        return card;
    }

    /**
     * 复制指定版本的文本
     * @param {string} index - 版本索引
     */
    async copyText(index) {
        if (!this.currentResults || !this.currentResults.versions) {
            return;
        }
        
        const versionIndex = parseInt(index) - 1;
        const version = this.currentResults.versions[versionIndex];
        
        if (!version) {
            return;
        }
        
        try {
            await navigator.clipboard.writeText(version.content);
            this.showNotification('success', '复制成功', `版本 ${index} 已复制到剪贴板`);
        } catch (error) {
            this.showNotification('error', '复制失败', '无法访问剪贴板');
        }
    }

    /**
     * 复制所有结果
     */
    async copyAllResults() {
        if (!this.currentResults || !this.currentResults.versions) {
            return;
        }
        
        const allText = this.currentResults.versions
            .map((version, index) => `=== ${version.title || `版本 ${index + 1}`} ===\n\n${version.content}`)
            .join('\n\n' + '='.repeat(50) + '\n\n');
        
        try {
            await navigator.clipboard.writeText(allText);
            this.showNotification('success', '复制成功', '所有版本已复制到剪贴板');
        } catch (error) {
            this.showNotification('error', '复制失败', '无法访问剪贴板');
        }
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + Enter: 开始润色
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.polishText();
        }
        
        // Ctrl/Cmd + ,: 打开设置
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            this.toggleSettings();
        }
        
        // Escape: 关闭设置面板
        if (e.key === 'Escape' && this.elements.settingsPanel.classList.contains('open')) {
            this.toggleSettings();
        }
    }

    /**
     * 更新API状态显示
     * @param {string} type - 状态类型 ('success', 'error', 'loading')
     * @param {string} message - 状态消息
     */
    updateAPIStatus(type, message) {
        const status = this.elements.apiStatus;
        status.className = `api-status ${type}`;
        status.textContent = message;
    }

    /**
     * 更新润色按钮状态
     */
    updatePolishButtonState() {
        const hasAPIKey = !!storageManager.loadAPIKey();
        this.elements.polishBtn.disabled = !hasAPIKey || this.isProcessing;
        
        if (!hasAPIKey) {
            this.elements.polishBtn.innerHTML = '<i class="fas fa-cog"></i> 请先配置API';
        } else {
            this.elements.polishBtn.innerHTML = '<i class="fas fa-magic"></i> 开始润色';
        }
    }

    /**
     * 显示/隐藏加载状态
     * @param {boolean} show - 是否显示
     */
    showLoading(show) {
        this.elements.loadingOverlay.classList.toggle('show', show);
    }

    /**
     * 显示通知
     * @param {string} type - 通知类型 ('success', 'error', 'warning')
     * @param {string} title - 通知标题
     * @param {string} message - 通知消息
     */
    showNotification(type, title, message) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle'
        };
        
        notification.innerHTML = `
            <i class="notification-icon ${iconMap[type]}"></i>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        this.elements.notificationContainer.appendChild(notification);
        
        // 自动移除通知
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new TextPolisherApp();
});
