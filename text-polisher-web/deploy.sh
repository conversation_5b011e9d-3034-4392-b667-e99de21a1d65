#!/bin/bash

# 学术文本润色助手 - 部署脚本
# 用于快速部署到各种静态网站托管服务

echo "🚀 学术文本润色助手 - 部署脚本"
echo "=================================="

# 检查必要文件
echo "📋 检查项目文件..."
required_files=("index.html" "css/style.css" "js/app.js" "js/gemini-api.js" "js/storage.js")

for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件检查完成"

# 创建部署目录
echo "📁 创建部署目录..."
DEPLOY_DIR="dist"
rm -rf $DEPLOY_DIR
mkdir -p $DEPLOY_DIR

# 复制文件
echo "📋 复制项目文件..."
cp -r index.html css js $DEPLOY_DIR/
cp README.md $DEPLOY_DIR/

echo "✅ 文件复制完成"

# 显示部署选项
echo ""
echo "🌐 部署选项："
echo "1. GitHub Pages"
echo "2. Netlify"
echo "3. Vercel"
echo "4. 本地预览"
echo ""

read -p "请选择部署方式 (1-4): " choice

case $choice in
    1)
        echo "📖 GitHub Pages 部署说明："
        echo "1. 将 dist/ 目录内容推送到 GitHub 仓库的 gh-pages 分支"
        echo "2. 在仓库设置中启用 GitHub Pages"
        echo "3. 选择 gh-pages 分支作为源"
        ;;
    2)
        echo "📖 Netlify 部署说明："
        echo "1. 访问 https://netlify.com"
        echo "2. 拖拽 dist/ 目录到 Netlify 部署区域"
        echo "3. 或连接 GitHub 仓库进行自动部署"
        ;;
    3)
        echo "📖 Vercel 部署说明："
        echo "1. 访问 https://vercel.com"
        echo "2. 导入 GitHub 仓库"
        echo "3. 设置构建输出目录为 dist/"
        ;;
    4)
        echo "🖥️  启动本地预览..."
        if command -v python3 &> /dev/null; then
            cd $DEPLOY_DIR
            echo "📱 本地服务器启动在: http://localhost:8000"
            python3 -m http.server 8000
        elif command -v python &> /dev/null; then
            cd $DEPLOY_DIR
            echo "📱 本地服务器启动在: http://localhost:8000"
            python -m SimpleHTTPServer 8000
        else
            echo "❌ 未找到 Python，请手动启动 HTTP 服务器"
            echo "💡 您可以使用任何静态文件服务器预览 dist/ 目录"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 部署准备完成！"
echo "📁 部署文件位于: $DEPLOY_DIR/"
echo ""
echo "📝 使用说明："
echo "1. 获取 Gemini API 密钥: https://makersuite.google.com/app/apikey"
echo "2. 在应用中配置 API 密钥"
echo "3. 开始使用学术文本润色功能"
echo ""
echo "🔗 更多信息请查看 README.md"
