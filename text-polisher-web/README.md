# 学术文本润色助手

一个基于Google Gemini AI的现代化Web应用，专门用于学术文本的智能润色和优化。

## ✨ 功能特色

- 🎯 **专业学术润色** - 内置专业的学术语言润色提示词，符合顶级期刊标准
- 🔄 **多版本输出** - 自动生成3个不同风格的润色版本供选择
- 🌓 **现代化界面** - 支持亮色/暗色主题切换，响应式设计
- 🔐 **安全存储** - API密钥本地加密存储，保护隐私安全
- ⚡ **快速响应** - 支持多种Gemini模型，平衡质量与速度
- 📱 **跨平台兼容** - 支持桌面和移动设备访问

## 🚀 快速开始

### 1. 获取Gemini API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录您的Google账户
3. 创建新的API密钥
4. 复制生成的API密钥（格式类似：`AIza...`）

### 2. 配置应用

1. 打开应用页面
2. 点击右上角的设置图标 ⚙️
3. 在"Gemini API 密钥"字段中粘贴您的API密钥
4. 选择合适的模型（推荐使用 Gemini 1.5 Pro）
5. 点击"保存设置"按钮
6. 可选：点击"测试连接"验证API密钥是否有效

### 3. 开始使用

1. 在左侧输入框中粘贴或输入需要润色的学术文本
2. 点击"开始润色"按钮
3. 等待AI处理（通常需要10-30秒）
4. 查看右侧生成的多个润色版本
5. 点击"复制"按钮复制喜欢的版本

## 🎯 使用技巧

### 输入文本建议
- **长度适中**：建议单次输入500-2000字符，效果最佳
- **完整段落**：尽量输入完整的段落或章节，避免断句
- **学术内容**：专门针对学术论文、研究报告等正式文本优化

### 模型选择指南
- **Gemini 1.5 Pro**：质量最高，适合重要文档的精细润色
- **Gemini 1.5 Flash**：速度最快，适合快速润色和初稿优化
- **Gemini Pro**：平衡选择，适合日常使用

### 键盘快捷键
- `Ctrl/Cmd + Enter`：开始润色
- `Ctrl/Cmd + ,`：打开设置
- `Escape`：关闭设置面板

## 🔧 技术特性

### 前端技术
- **纯原生实现**：HTML5 + CSS3 + JavaScript ES6+
- **现代CSS**：CSS Grid、Flexbox、CSS变量
- **响应式设计**：适配各种屏幕尺寸
- **无框架依赖**：轻量级，加载速度快

### 安全特性
- **本地存储**：API密钥仅存储在用户浏览器本地
- **加密处理**：使用Base64编码保护敏感信息
- **无服务器**：直接调用Google API，无中间服务器

### 兼容性
- **现代浏览器**：Chrome 80+、Firefox 75+、Safari 13+、Edge 80+
- **移动设备**：iOS Safari、Android Chrome
- **操作系统**：Windows、macOS、Linux、iOS、Android

## 📁 项目结构

```
text-polisher-web/
├── index.html          # 主页面
├── css/
│   └── style.css       # 样式文件
├── js/
│   ├── app.js          # 主应用逻辑
│   ├── gemini-api.js   # API调用模块
│   └── storage.js      # 本地存储管理
└── README.md           # 说明文档
```

## 🎨 自定义配置

### 主题定制
应用支持亮色和暗色两种主题，您可以通过修改CSS变量来自定义颜色：

```css
:root {
    --accent-color: #3b82f6;    /* 主色调 */
    --bg-primary: #ffffff;      /* 主背景色 */
    --text-primary: #1e293b;    /* 主文字色 */
    /* ... 更多变量 */
}
```

### 提示词定制
如需修改内置的学术润色提示词，请编辑 `js/gemini-api.js` 文件中的 `academicPrompt` 变量。

## 🔍 故障排除

### 常见问题

**Q: API密钥无效怎么办？**
A: 请确认API密钥格式正确（以"AIza"开头），并检查是否已启用Gemini API服务。

**Q: 润色速度很慢？**
A: 可以尝试切换到Gemini 1.5 Flash模型，或减少输入文本长度。

**Q: 无法复制结果？**
A: 请确保浏览器允许访问剪贴板，或手动选择文本复制。

**Q: 移动端显示异常？**
A: 请使用现代移动浏览器，并确保网络连接稳定。

### 错误代码说明
- `400 Bad Request`：请求格式错误，检查API密钥
- `403 Forbidden`：API密钥无权限或配额不足
- `429 Too Many Requests`：请求过于频繁，请稍后重试
- `500 Internal Server Error`：Google服务器错误，请稍后重试

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**：本应用需要有效的Google Gemini API密钥才能正常工作。请确保您已获得相应的API访问权限。
