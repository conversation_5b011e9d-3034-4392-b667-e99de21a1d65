# ===============================================================================
# CONSOLIDATED PROSODY ANALYSIS: Chinese Phrase "mei-zhi mao chi-zhe yi pen mao liang"
# Publication-Quality Visualization Following Academic Standards
# ===============================================================================
# 
# This script analyzes three prosodic conditions:
# - Condition 1: Neutral prosody (no stress emphasis)
# - Condition 2: Subject stress (emphasis on "mei-zhi mao")  
# - Condition 3: Object stress (emphasis on "yi pen mao liang")
#
# Follows visualization standards from Journal of Phonetics, Language and Speech
# ===============================================================================

# 定义一个包的列表
required_packages <- c("readxl", "ggplot2", "dplyr", "scales", "gridExtra", 
                       "cowplot", "viridis", "RColorBrewer")

# 检查每个包是否已安装
for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    # 如果包未安装，则安装包
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}



# Load required libraries
library(readxl)
library(ggplot2)
library(dplyr)
library(scales)
library(gridExtra)
library(cowplot)
library(viridis)
library(RColorBrewer)

# Set working directory to script location
script_dir <- dirname(rstudioapi::getActiveDocumentContext()$path)
setwd(script_dir)

# ===============================================================================
# ACADEMIC COLOR PALETTE (Based on phonetics journal standards)
# ===============================================================================

# Professional color scheme for prosody visualization
academic_colors <- list(
  neutral = "#2C3E50",      # Dark blue-gray for neutral condition
  subject = "#E74C3C",      # Red for subject emphasis
  object = "#27AE60",       # Green for object emphasis
  background = "#ECF0F1",   # Light gray background
  grid = "#BDC3C7",         # Medium gray for grid lines
  text = "#2C3E50",         # Dark text
  accent = "#F39C12"        # Orange for highlights
)

# ===============================================================================
# DATA LOADING AND PREPARATION
# ===============================================================================

# Load data from all three conditions
load_prosody_data <- function() {
  
  # Condition 1: Neutral prosody
  cat("Loading Condition 1 (Neutral prosody)...\n")
  data1 <- read_excel("Ex1/normf1.xlsx")
  data1$point <- as.numeric(data1$point)
  data1$condition <- "Neutral"
  data1$condition_label <- "Neutral Prosody"
  
  # Condition 2: Subject stress
  cat("Loading Condition 2 (Subject stress)...\n")
  data2 <- read_excel("Ex2/normf2.xlsx")
  data2$point <- as.numeric(data2$point)
  data2$condition <- "Subject"
  data2$condition_label <- "Subject Stress (mei-zhi mao)"
  
  # Condition 3: Object stress
  cat("Loading Condition 3 (Object stress)...\n")
  data3 <- read_excel("Ex3/normf3.xlsx")
  data3$point <- as.numeric(data3$point)
  data3$condition <- "Object"
  data3$condition_label <- "Object Stress (yi pen mao liang)"
  
  # Combine all data
  combined_data <- rbind(data1, data2, data3)
  combined_data$condition <- factor(combined_data$condition, 
                                   levels = c("Neutral", "Subject", "Object"))
  
  return(list(
    data1 = data1,
    data2 = data2, 
    data3 = data3,
    combined = combined_data
  ))
}

# ===============================================================================
# STATISTICAL ANALYSIS FUNCTIONS
# ===============================================================================

# Calculate comprehensive pitch statistics
calculate_pitch_statistics <- function(data, condition_name) {
  
  stats <- data %>%
    summarise(
      condition = condition_name,
      n_points = n(),
      mean_f0 = mean(f00, na.rm = TRUE),
      median_f0 = median(f00, na.rm = TRUE),
      sd_f0 = sd(f00, na.rm = TRUE),
      min_f0 = min(f00, na.rm = TRUE),
      max_f0 = max(f00, na.rm = TRUE),
      range_f0 = max_f0 - min_f0,
      q25_f0 = quantile(f00, 0.25, na.rm = TRUE),
      q75_f0 = quantile(f00, 0.75, na.rm = TRUE),
      iqr_f0 = q75_f0 - q25_f0,
      cv_f0 = (sd_f0 / mean_f0) * 100  # Coefficient of variation
    )
  
  return(stats)
}

# Analyze focal regions for stress conditions
analyze_focal_regions <- function(data, focal_start, focal_end, condition_name) {
  
  focal_data <- data %>%
    filter(point >= focal_start & point <= focal_end)
  
  non_focal_data <- data %>%
    filter(point < focal_start | point > focal_end)
  
  focal_stats <- focal_data %>%
    summarise(
      focal_mean = mean(f00, na.rm = TRUE),
      focal_max = max(f00, na.rm = TRUE),
      focal_min = min(f00, na.rm = TRUE),
      focal_range = focal_max - focal_min
    )
  
  non_focal_stats <- non_focal_data %>%
    summarise(
      non_focal_mean = mean(f00, na.rm = TRUE)
    )
  
  prominence <- focal_stats$focal_mean - non_focal_stats$non_focal_mean
  prominence_percent <- (prominence / non_focal_stats$non_focal_mean) * 100
  
  return(list(
    condition = condition_name,
    focal_mean = focal_stats$focal_mean,
    non_focal_mean = non_focal_stats$non_focal_mean,
    prominence = prominence,
    prominence_percent = prominence_percent,
    focal_range = focal_stats$focal_range
  ))
}

# ===============================================================================
# VISUALIZATION FUNCTIONS
# ===============================================================================

# Create academic-style theme
create_academic_theme <- function(base_size = 12) {
  theme_classic(base_size = base_size) +
    theme(
      # Panel and background
      panel.background = element_rect(fill = "white", color = NA),
      plot.background = element_rect(fill = "white", color = NA),
      
      # Grid lines (minimal, academic style)
      panel.grid.major.y = element_line(color = academic_colors$grid, 
                                       linewidth = 0.3, linetype = "dotted"),
      panel.grid.minor = element_blank(),
      panel.grid.major.x = element_blank(),
      
      # Axes
      axis.line = element_line(color = academic_colors$text, linewidth = 0.8),
      axis.ticks = element_line(color = academic_colors$text, linewidth = 0.5),
      axis.ticks.length = unit(0.15, "cm"),
      
      # Text formatting
      axis.text.x = element_text(size = base_size - 1, color = academic_colors$text, 
                                margin = margin(t = 5)),
      axis.text.y = element_text(size = base_size - 1, color = academic_colors$text, 
                                margin = margin(r = 5)),
      axis.title.x = element_text(size = base_size + 1, color = academic_colors$text, 
                                 margin = margin(t = 10)),
      axis.title.y = element_text(size = base_size + 1, color = academic_colors$text, 
                                 margin = margin(r = 10)),
      
      # Plot margins
      plot.margin = margin(15, 15, 15, 15),
      
      # Title formatting
      plot.title = element_text(size = base_size + 2, face = "bold", hjust = 0.5, 
                               margin = margin(b = 15), color = academic_colors$text),
      plot.subtitle = element_text(size = base_size, hjust = 0.5, 
                                  margin = margin(b = 10), color = academic_colors$text),
      
      # Legend
      legend.background = element_rect(fill = "white", color = NA),
      legend.key = element_rect(fill = "white", color = NA),
      legend.text = element_text(size = base_size - 1),
      legend.title = element_text(size = base_size, face = "bold"),
      legend.position = "bottom"
    )
}

# Define syllable information
syllable_labels <- c("mei", "zhi", "mao", "chi", "zhe", "yi", "pen", "mao", "liang")
syllable_positions <- seq(1, 9, 1)
syllable_boundaries <- seq(0.5, 9.5, 1)

# ===============================================================================
# MAIN ANALYSIS EXECUTION
# ===============================================================================

# Load all data
cat("=== LOADING PROSODY DATA ===\n")
prosody_data <- load_prosody_data()

# Calculate statistics for each condition
cat("\n=== CALCULATING STATISTICS ===\n")
stats_neutral <- calculate_pitch_statistics(prosody_data$data1, "Neutral")
stats_subject <- calculate_pitch_statistics(prosody_data$data2, "Subject Stress")
stats_object <- calculate_pitch_statistics(prosody_data$data3, "Object Stress")

# Analyze focal regions
focal_subject <- analyze_focal_regions(prosody_data$data2, 0.5, 3.5, "Subject Stress")
focal_object <- analyze_focal_regions(prosody_data$data3, 5.5, 9.5, "Object Stress")

# Print comprehensive statistics
cat("\n", paste(rep("=", 80), collapse = ""), "\n")
cat("COMPREHENSIVE PROSODIC ANALYSIS RESULTS\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
cat("Phrase: mei-zhi mao chi-zhe yi pen mao liang\n")
cat("Total syllables:", length(syllable_labels), "\n\n")

# Print basic statistics
all_stats <- rbind(stats_neutral, stats_subject, stats_object)
print(all_stats)

cat("\nFOCAL REGION ANALYSIS:\n")
cat(sprintf("Subject stress prominence: +%.1f Hz (%.1f%% increase)\n",
           focal_subject$prominence, focal_subject$prominence_percent))
cat(sprintf("Object stress prominence: +%.1f Hz (%.1f%% increase)\n",
           focal_object$prominence, focal_object$prominence_percent))

# ===============================================================================
# INDIVIDUAL PLOT GENERATION
# ===============================================================================

# Function to create individual condition plots
create_condition_plot <- function(data, condition_name, color, focal_start = NULL, focal_end = NULL) {

  # Base plot
  p <- ggplot(data, aes(x = point, y = f00)) +

    # Add focal region highlighting if specified
    {if (!is.null(focal_start) && !is.null(focal_end)) {
      annotate("rect", xmin = focal_start, xmax = focal_end,
               ymin = -Inf, ymax = Inf,
               fill = color, alpha = 0.15)
    }} +

    # Main contour line
    geom_line(color = color, linewidth = 1.2, alpha = 0.9) +

    # Data points
    geom_point(color = color, size = 2, alpha = 0.8) +

    # Syllable boundaries
    geom_vline(xintercept = syllable_boundaries,
               color = academic_colors$grid, linewidth = 0.3, linetype = "dashed") +

    # Syllable labels
    annotate("text", x = syllable_positions, y = max(data$f00) * 1.05,
             label = syllable_labels, size = 3.5, color = academic_colors$text,
             fontface = "italic") +

    # Scales and labels
    scale_x_continuous(name = "Syllable Position",
                      breaks = syllable_positions,
                      labels = syllable_labels,
                      limits = c(0.5, 9.5)) +
    scale_y_continuous(name = "Fundamental Frequency (Hz)",
                      breaks = pretty_breaks(n = 6)) +

    # Title and theme
    ggtitle(paste("Condition:", condition_name)) +
    create_academic_theme(base_size = 11)

  # Add focal region annotation if specified
  if (!is.null(focal_start) && !is.null(focal_end)) {
    focal_label <- if (focal_start < 4) "Subject Focus" else "Object Focus"
    p <- p + annotate("text", x = (focal_start + focal_end) / 2,
                     y = min(data$f00) * 0.95,
                     label = focal_label, size = 3,
                     color = color, fontface = "bold")
  }

  return(p)
}

# Generate individual plots
cat("\n=== GENERATING INDIVIDUAL PLOTS ===\n")

# Plot 1: Neutral condition
plot1 <- create_condition_plot(prosody_data$data1, "Neutral Prosody",
                              academic_colors$neutral)

# Plot 2: Subject stress condition
plot2 <- create_condition_plot(prosody_data$data2, "Subject Stress",
                              academic_colors$subject, 0.5, 3.5)

# Plot 3: Object stress condition
plot3 <- create_condition_plot(prosody_data$data3, "Object Stress",
                              academic_colors$object, 5.5, 9.5)

# ===============================================================================
# COMPARATIVE VISUALIZATION
# ===============================================================================

# Create overlay comparison plot
create_comparison_plot <- function(combined_data) {

  # Define colors for each condition
  condition_colors <- c("Neutral" = academic_colors$neutral,
                       "Subject" = academic_colors$subject,
                       "Object" = academic_colors$object)

  p <- ggplot(combined_data, aes(x = point, y = f00, color = condition)) +

    # Focal region annotations
    annotate("rect", xmin = 0.5, xmax = 3.5, ymin = -Inf, ymax = Inf,
             fill = academic_colors$subject, alpha = 0.08) +
    annotate("rect", xmin = 5.5, xmax = 9.5, ymin = -Inf, ymax = Inf,
             fill = academic_colors$object, alpha = 0.08) +

    # Contour lines
    geom_line(aes(group = condition), linewidth = 1.1, alpha = 0.85) +

    # Data points
    geom_point(size = 1.8, alpha = 0.8) +

    # Syllable boundaries
    geom_vline(xintercept = syllable_boundaries,
               color = academic_colors$grid, linewidth = 0.3, linetype = "dashed") +

    # Syllable labels
    annotate("text", x = syllable_positions,
             y = max(combined_data$f00) * 1.05,
             label = syllable_labels, size = 3.5,
             color = academic_colors$text, fontface = "italic") +

    # Focal region labels
    annotate("text", x = 2, y = min(combined_data$f00) * 0.95,
             label = "Subject Focus", size = 3,
             color = academic_colors$subject, fontface = "bold") +
    annotate("text", x = 7.5, y = min(combined_data$f00) * 0.95,
             label = "Object Focus", size = 3,
             color = academic_colors$object, fontface = "bold") +

    # Scales and colors
    scale_color_manual(values = condition_colors,
                      name = "Prosodic Condition",
                      labels = c("Neutral", "Subject Stress", "Object Stress")) +
    scale_x_continuous(name = "Syllable Position",
                      breaks = syllable_positions,
                      labels = syllable_labels,
                      limits = c(0.5, 9.5)) +
    scale_y_continuous(name = "Fundamental Frequency (Hz)",
                      breaks = pretty_breaks(n = 6)) +

    # Title and theme
    ggtitle("Prosodic Comparison: Chinese Phrase Analysis",
            subtitle = "mei-zhi mao chi-zhe yi pen mao liang") +
    create_academic_theme(base_size = 12) +

    # Legend positioning
    theme(legend.position = "bottom",
          legend.box = "horizontal",
          legend.margin = margin(t = 15))

  return(p)
}

# Generate comparison plot
cat("Generating comparative visualization...\n")
comparison_plot <- create_comparison_plot(prosody_data$combined)

# ===============================================================================
# STATISTICAL SUMMARY VISUALIZATION
# ===============================================================================

# Create statistical summary plot
create_stats_plot <- function(all_stats) {

  # Prepare data for plotting
  stats_long <- all_stats %>%
    select(condition, mean_f0, range_f0, cv_f0) %>%
    tidyr::pivot_longer(cols = c(mean_f0, range_f0, cv_f0),
                       names_to = "metric", values_to = "value")

  # Define metric labels
  metric_labels <- c("mean_f0" = "Mean F0 (Hz)",
                    "range_f0" = "F0 Range (Hz)",
                    "cv_f0" = "Coefficient of Variation (%)")

  stats_long$metric_label <- metric_labels[stats_long$metric]

  # Create plot
  p <- ggplot(stats_long, aes(x = condition, y = value, fill = condition)) +
    geom_col(alpha = 0.8, width = 0.7) +
    facet_wrap(~metric_label, scales = "free_y", ncol = 3) +
    scale_fill_manual(values = c("Neutral" = academic_colors$neutral,
                                "Subject Stress" = academic_colors$subject,
                                "Object Stress" = academic_colors$object)) +
    labs(title = "Prosodic Statistics Comparison",
         x = "Condition", y = "Value") +
    create_academic_theme(base_size = 10) +
    theme(legend.position = "none",
          axis.text.x = element_text(angle = 45, hjust = 1),
          strip.background = element_rect(fill = academic_colors$background),
          strip.text = element_text(face = "bold"))

  return(p)
}

# Generate statistics plot
stats_plot <- create_stats_plot(all_stats)

# ===============================================================================
# SAVE ALL VISUALIZATIONS
# ===============================================================================

cat("\n=== SAVING VISUALIZATIONS ===\n")

# Create output directory if it doesn't exist
output_dir <- "prosody_analysis_output"
if (!dir.exists(output_dir)) {
  dir.create(output_dir)
  cat("Created output directory:", output_dir, "\n")
}

# Save individual condition plots
ggsave(file.path(output_dir, "01_neutral_prosody.png"), plot1,
       width = 10, height = 6, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "01_neutral_prosody.pdf"), plot1,
       width = 10, height = 6, bg = "white")

ggsave(file.path(output_dir, "02_subject_stress.png"), plot2,
       width = 10, height = 6, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "02_subject_stress.pdf"), plot2,
       width = 10, height = 6, bg = "white")

ggsave(file.path(output_dir, "03_object_stress.png"), plot3,
       width = 10, height = 6, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "03_object_stress.pdf"), plot3,
       width = 10, height = 6, bg = "white")

# Save comparison plot
ggsave(file.path(output_dir, "04_prosody_comparison.png"), comparison_plot,
       width = 12, height = 8, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "04_prosody_comparison.pdf"), comparison_plot,
       width = 12, height = 8, bg = "white")

# Save statistics plot
ggsave(file.path(output_dir, "05_statistical_summary.png"), stats_plot,
       width = 12, height = 6, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "05_statistical_summary.pdf"), stats_plot,
       width = 12, height = 6, bg = "white")

# Create combined figure for publication
combined_figure <- plot_grid(
  plot_grid(plot1, plot2, plot3, ncol = 1, labels = c("A", "B", "C")),
  comparison_plot,
  ncol = 2, labels = c("", "D"), rel_widths = c(1, 1.2)
)

ggsave(file.path(output_dir, "06_publication_figure.png"), combined_figure,
       width = 16, height = 12, dpi = 300, bg = "white")
ggsave(file.path(output_dir, "06_publication_figure.pdf"), combined_figure,
       width = 16, height = 12, bg = "white")

cat("Individual plots saved with descriptive filenames\n")
cat("Comparison plot saved for cross-condition analysis\n")
cat("Publication-ready combined figure created\n")

# ===============================================================================
# EXPORT STATISTICAL RESULTS
# ===============================================================================

cat("\n=== EXPORTING STATISTICAL RESULTS ===\n")

# Prepare comprehensive results table
results_summary <- data.frame(
  Condition = c("Neutral", "Subject Stress", "Object Stress"),
  Mean_F0_Hz = round(c(stats_neutral$mean_f0, stats_subject$mean_f0, stats_object$mean_f0), 1),
  SD_F0_Hz = round(c(stats_neutral$sd_f0, stats_subject$sd_f0, stats_object$sd_f0), 1),
  Range_Hz = round(c(stats_neutral$range_f0, stats_subject$range_f0, stats_object$range_f0), 1),
  CV_Percent = round(c(stats_neutral$cv_f0, stats_subject$cv_f0, stats_object$cv_f0), 1),
  Focal_Prominence_Hz = c(NA, round(focal_subject$prominence, 1), round(focal_object$prominence, 1)),
  Prominence_Percent = c(NA, round(focal_subject$prominence_percent, 1), round(focal_object$prominence_percent, 1))
)

# Save results to CSV
write.csv(results_summary, file.path(output_dir, "prosody_statistics.csv"), row.names = FALSE)

# Save detailed analysis report
sink(file.path(output_dir, "analysis_report.txt"))
cat("PROSODIC ANALYSIS REPORT\n")
cat("========================\n\n")
cat("Phrase: mei-zhi mao chi-zhe yi pen mao liang\n")
cat("Analysis Date:", format(Sys.Date(), "%Y-%m-%d"), "\n\n")

cat("EXPERIMENTAL CONDITIONS:\n")
cat("1. Neutral: No stress emphasis\n")
cat("2. Subject Stress: Emphasis on 'mei-zhi mao' (syllables 1-3)\n")
cat("3. Object Stress: Emphasis on 'yi pen mao liang' (syllables 6-9)\n\n")

cat("STATISTICAL SUMMARY:\n")
print(results_summary)

cat("\n\nFOCAL REGION ANALYSIS:\n")
cat("Subject stress creates", round(focal_subject$prominence, 1), "Hz prominence\n")
cat("Object stress creates", round(focal_object$prominence, 1), "Hz prominence\n")

cat("\n\nFILES GENERATED:\n")
cat("- Individual condition plots (PNG & PDF)\n")
cat("- Comparative visualization\n")
cat("- Statistical summary plot\n")
cat("- Publication-ready combined figure\n")
cat("- Statistical results (CSV)\n")
cat("- This analysis report\n")
sink()

cat("Statistical results exported to CSV\n")
cat("Comprehensive analysis report saved\n")

# ===============================================================================
# FINAL SUMMARY
# ===============================================================================

cat("\n", paste(rep("=", 80), collapse = ""), "\n")
cat("ANALYSIS COMPLETE - PUBLICATION-READY OUTPUTS GENERATED\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
cat("All files saved to:", file.path(getwd(), output_dir), "\n")
cat("Ready for inclusion in phonetics research publications\n")
cat(paste(rep("=", 80), collapse = ""), "\n")
