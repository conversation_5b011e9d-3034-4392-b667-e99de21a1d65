//
//  AppSettings.swift
//  TextPolisher
//
//  Created by AI Assistant on 2025-07-30.
//

import Foundation
import SwiftUI

/// 应用设置管理类
class AppSettings: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 选中的Gemini模型
    @Published var selectedModel: GeminiModel = .geminiPro
    
    /// API密钥是否已配置
    @Published var isAPIKeyConfigured: Bool = false
    
    /// 是否显示设置页面
    @Published var showingSettings: Bool = false
    
    /// 当前API密钥（仅用于显示状态，不存储实际值）
    @Published var apiKeyStatus: String = "未配置"
    
    // MARK: - Private Properties
    
    private let keychainService = KeychainService()
    private let userDefaults = UserDefaults.standard
    
    // MARK: - UserDefaults Keys
    
    private enum UserDefaultsKeys {
        static let selectedModel = "selectedModel"
        static let isAPIKeyConfigured = "isAPIKeyConfigured"
    }
    
    // MARK: - Initialization
    
    init() {
        loadSettings()
        checkAPIKeyStatus()
    }
    
    // MARK: - Public Methods
    
    /// 保存API密钥
    /// - Parameter apiKey: API密钥
    /// - Returns: 保存是否成功
    func saveAPIKey(_ apiKey: String) -> Bool {
        let success = keychainService.save(apiKey, forKey: KeychainService.Keys.geminiAPIKey)
        if success {
            isAPIKeyConfigured = true
            apiKeyStatus = "已配置 (••••••••)"
            userDefaults.set(true, forKey: UserDefaultsKeys.isAPIKeyConfigured)
        }
        return success
    }
    
    /// 获取API密钥
    /// - Returns: API密钥，如果不存在则返回nil
    func getAPIKey() -> String? {
        return keychainService.load(forKey: KeychainService.Keys.geminiAPIKey)
    }
    
    /// 删除API密钥
    /// - Returns: 删除是否成功
    func deleteAPIKey() -> Bool {
        let success = keychainService.delete(forKey: KeychainService.Keys.geminiAPIKey)
        if success {
            isAPIKeyConfigured = false
            apiKeyStatus = "未配置"
            userDefaults.set(false, forKey: UserDefaultsKeys.isAPIKeyConfigured)
        }
        return success
    }
    
    /// 保存选中的模型
    /// - Parameter model: Gemini模型
    func saveSelectedModel(_ model: GeminiModel) {
        selectedModel = model
        userDefaults.set(model.rawValue, forKey: UserDefaultsKeys.selectedModel)
    }
    
    /// 验证API密钥格式
    /// - Parameter apiKey: API密钥
    /// - Returns: 是否为有效格式
    func validateAPIKey(_ apiKey: String) -> Bool {
        // Gemini API密钥通常以"AIza"开头，长度约为39个字符
        return apiKey.hasPrefix("AIza") && apiKey.count >= 35
    }
    
    // MARK: - Private Methods
    
    /// 加载设置
    private func loadSettings() {
        // 加载选中的模型
        if let modelRawValue = userDefaults.object(forKey: UserDefaultsKeys.selectedModel) as? String,
           let model = GeminiModel(rawValue: modelRawValue) {
            selectedModel = model
        }
        
        // 加载API密钥配置状态
        isAPIKeyConfigured = userDefaults.bool(forKey: UserDefaultsKeys.isAPIKeyConfigured)
    }
    
    /// 检查API密钥状态
    private func checkAPIKeyStatus() {
        if keychainService.load(forKey: KeychainService.Keys.geminiAPIKey) != nil {
            isAPIKeyConfigured = true
            apiKeyStatus = "已配置 (••••••••)"
        } else {
            isAPIKeyConfigured = false
            apiKeyStatus = "未配置"
        }
    }
}

// MARK: - Extensions

extension AppSettings {
    
    /// 重置所有设置
    func resetAllSettings() {
        _ = deleteAPIKey()
        selectedModel = .geminiPro
        userDefaults.removeObject(forKey: UserDefaultsKeys.selectedModel)
        userDefaults.removeObject(forKey: UserDefaultsKeys.isAPIKeyConfigured)
    }
    
    /// 导出设置（不包含敏感信息）
    func exportSettings() -> [String: Any] {
        return [
            "selectedModel": selectedModel.rawValue,
            "isAPIKeyConfigured": isAPIKeyConfigured
        ]
    }
}
