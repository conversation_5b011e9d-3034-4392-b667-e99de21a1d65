//
//  GeminiModels.swift
//  TextPolisher
//
//  Created by AI Assistant on 2025-07-30.
//

import Foundation

// MARK: - Gemini API Request Models

/// Gemini API请求体结构
struct GeminiRequest: Codable {
    let contents: [Content]
    let generationConfig: GenerationConfig?
    let safetySettings: [SafetySetting]?
    
    init(prompt: String, generationConfig: GenerationConfig? = nil) {
        self.contents = [Content(parts: [Part(text: prompt)])]
        self.generationConfig = generationConfig ?? GenerationConfig()
        self.safetySettings = SafetySetting.defaultSettings
    }
}

/// 内容结构
struct Content: Codable {
    let parts: [Part]
    let role: String?
    
    init(parts: [Part], role: String? = nil) {
        self.parts = parts
        self.role = role
    }
}

/// 文本部分结构
struct Part: Codable {
    let text: String
}

/// 生成配置
struct GenerationConfig: Codable {
    let temperature: Double
    let topK: Int
    let topP: Double
    let maxOutputTokens: Int
    let stopSequences: [String]?
    
    init(temperature: Double = 0.7,
         topK: Int = 40,
         topP: Double = 0.95,
         maxOutputTokens: Int = 8192,
         stopSequences: [String]? = nil) {
        self.temperature = temperature
        self.topK = topK
        self.topP = topP
        self.maxOutputTokens = maxOutputTokens
        self.stopSequences = stopSequences
    }
}

/// 安全设置
struct SafetySetting: Codable {
    let category: String
    let threshold: String
    
    static let defaultSettings: [SafetySetting] = [
        SafetySetting(category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
        SafetySetting(category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
        SafetySetting(category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_MEDIUM_AND_ABOVE"),
        SafetySetting(category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_MEDIUM_AND_ABOVE")
    ]
}

// MARK: - Gemini API Response Models

/// Gemini API响应结构
struct GeminiResponse: Codable {
    let candidates: [Candidate]?
    let promptFeedback: PromptFeedback?
    let error: GeminiError?
}

/// 候选响应
struct Candidate: Codable {
    let content: Content?
    let finishReason: String?
    let index: Int?
    let safetyRatings: [SafetyRating]?
}

/// 提示反馈
struct PromptFeedback: Codable {
    let safetyRatings: [SafetyRating]?
    let blockReason: String?
}

/// 安全评级
struct SafetyRating: Codable {
    let category: String
    let probability: String
}

/// Gemini API错误结构
struct GeminiError: Codable {
    let code: Int
    let message: String
    let status: String?
}

// MARK: - Gemini Models Enum

/// 支持的Gemini模型
enum GeminiModel: String, CaseIterable, Identifiable {
    case geminiPro = "gemini-1.5-pro-latest"
    case geminiFlash = "gemini-1.5-flash-latest"
    case geminiProVision = "gemini-pro-vision"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .geminiPro:
            return "Gemini 1.5 Pro (推荐)"
        case .geminiFlash:
            return "Gemini 1.5 Flash (快速)"
        case .geminiProVision:
            return "Gemini Pro Vision"
        }
    }
    
    var description: String {
        switch self {
        case .geminiPro:
            return "最强大的模型，适合复杂的文本润色任务"
        case .geminiFlash:
            return "快速响应，适合简单的文本处理"
        case .geminiProVision:
            return "支持图像和文本的多模态模型"
        }
    }
}
